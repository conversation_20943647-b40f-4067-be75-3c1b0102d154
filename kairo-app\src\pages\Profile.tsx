import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { User, Award, TrendingUp, Calendar, Settings, LogOut, Edit } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';

export const Profile: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  const achievements = [
    { name: 'First Note', description: 'Created your first note', icon: '📝', unlocked: true, date: '2024-01-15' },
    { name: 'Quiz Master', description: 'Completed 10 quizzes', icon: '🧠', unlocked: true, date: '2024-01-18' },
    { name: 'Streak Keeper', description: '7-day login streak', icon: '🔥', unlocked: false },
    { name: 'Knowledge Vault', description: 'Created 50 notes', icon: '📚', unlocked: false },
    { name: 'Scholar', description: 'Reached level 10', icon: '🎓', unlocked: false },
    { name: 'Researcher', description: 'Added 100 sources', icon: '🔍', unlocked: false },
  ];

  const stats = [
    { label: 'Total XP', value: user?.xp || 0, change: '+150 this week' },
    { label: 'Level', value: user?.level || 1, change: 'Next: 180 XP' },
    { label: 'Notes Created', value: 23, change: '+3 this week' },
    { label: 'Quizzes Completed', value: 15, change: '+2 this week' },
    { label: 'Study Streak', value: '5 days', change: 'Personal best: 12' },
    { label: 'Topics Explored', value: 8, change: '+1 this month' },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-start justify-between">
        <div className="flex items-center space-x-6">
          <div className="relative">
            <div className="w-24 h-24 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full flex items-center justify-center">
              <User className="w-12 h-12 text-white" />
            </div>
            <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center border-2 border-gray-700 hover:bg-gray-700 transition-colors">
              <Edit className="w-4 h-4 text-gray-300" />
            </button>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">{user?.displayName}</h1>
            <p className="text-gray-400">@{user?.username}</p>
            <p className="text-gray-300 mt-2">Passionate learner exploring the depths of knowledge</p>
            <div className="flex items-center space-x-4 mt-3">
              <div className="flex items-center space-x-1 text-sm text-gray-400">
                <Calendar className="w-4 h-4" />
                <span>Joined January 2024</span>
              </div>
              <div className="flex items-center space-x-1 text-sm">
                <span className="text-purple-400 font-medium">Level {user?.level}</span>
                <span className="text-gray-400">•</span>
                <span className="text-yellow-400 font-medium">{user?.xp} XP</span>
              </div>
            </div>
          </div>
        </div>
        <div className="flex space-x-2">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="flex items-center space-x-2 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-300 hover:text-white transition-colors"
          >
            <Settings className="w-4 h-4" />
            <span>Settings</span>
          </motion.button>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={logout}
            className="flex items-center space-x-2 px-4 py-2 bg-red-600/20 border border-red-500/30 rounded-lg text-red-400 hover:bg-red-600/30 transition-colors"
          >
            <LogOut className="w-4 h-4" />
            <span>Logout</span>
          </motion.button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-gray-800">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: TrendingUp },
            { id: 'achievements', label: 'Achievements', icon: Award },
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-3 px-1 border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-purple-500 text-purple-400'
                    : 'border-transparent text-gray-400 hover:text-white'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          {/* XP Progress */}
          <div className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700">
            <h2 className="text-xl font-semibold text-white mb-4">Level Progress</h2>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-gray-300">Level {user?.level}</span>
                <span className="text-gray-300">Level {(user?.level || 1) + 1}</span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-3">
                <motion.div
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: '65%' }}
                  transition={{ duration: 1, ease: "easeOut" }}
                />
              </div>
              <div className="flex items-center justify-between text-sm text-gray-400">
                <span>{user?.xp} XP</span>
                <span>180 XP to next level</span>
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700"
              >
                <h3 className="text-gray-400 text-sm font-medium">{stat.label}</h3>
                <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                <p className="text-green-400 text-xs mt-2">{stat.change}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {activeTab === 'achievements' && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {achievements.map((achievement, index) => (
              <motion.div
                key={achievement.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`p-6 rounded-xl border transition-all duration-200 ${
                  achievement.unlocked
                    ? 'bg-yellow-500/10 border-yellow-500/30 hover:border-yellow-500/50'
                    : 'bg-gray-800/50 border-gray-700 hover:border-gray-600'
                }`}
              >
                <div className="text-center">
                  <div className="text-4xl mb-3">{achievement.icon}</div>
                  <h3 className={`font-semibold mb-2 ${
                    achievement.unlocked ? 'text-yellow-400' : 'text-gray-400'
                  }`}>
                    {achievement.name}
                  </h3>
                  <p className="text-gray-400 text-sm mb-3">{achievement.description}</p>
                  {achievement.unlocked && achievement.date && (
                    <p className="text-yellow-400 text-xs">
                      Unlocked {new Date(achievement.date).toLocaleDateString()}
                    </p>
                  )}
                  {!achievement.unlocked && (
                    <div className="w-full bg-gray-700 rounded-full h-2 mt-3">
                      <div className="bg-gray-600 h-2 rounded-full w-1/3"></div>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}
    </div>
  );
};