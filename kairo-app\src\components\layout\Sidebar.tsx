import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Home, FolderOpen, Brain, Award, User, ChevronLeft, ChevronRight, Map, Lightbulb } from 'lucide-react';
import { useLocation, Link } from 'react-router-dom';

export const Sidebar: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const location = useLocation();

  const menuItems = [
    { icon: Home, label: 'Dashboard', path: '/dashboard' },
    { icon: FolderOpen, label: 'My Vault', path: '/vault' },
    { icon: Brain, label: 'Mind Games', path: '/quiz', comingSoon: true },
    { icon: Map, label: 'Visual Maps', path: '/maps', comingSoon: true },
    { icon: Award, label: 'Achievements', path: '/achievements', comingSoon: true },
    { icon: Lightbulb, label: 'AI Summarizer', path: '/summarizer', comingSoon: true },
    { icon: User, label: 'Profile', path: '/profile' },
  ];

  return (
    <motion.div
      animate={{ width: collapsed ? 80 : 280 }}
      className="bg-gray-900/95 backdrop-blur-lg border-r border-gray-800 h-full flex flex-col"
    >
      <div className="p-4 border-b border-gray-800">
        <button
          onClick={() => setCollapsed(!collapsed)}
          className="w-full flex items-center justify-between text-gray-400 hover:text-white transition-colors"
        >
          <AnimatePresence>
            {!collapsed && (
              <motion.span
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="font-medium"
              >
                Navigation
              </motion.span>
            )}
          </AnimatePresence>
          {collapsed ? <ChevronRight className="w-5 h-5" /> : <ChevronLeft className="w-5 h-5" />}
        </button>
      </div>

      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            
            return (
              <li key={item.path}>
                <Link
                  to={item.comingSoon ? '#' : item.path}
                  className={`
                    flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200
                    ${isActive 
                      ? 'bg-purple-600 text-white' 
                      : 'text-gray-400 hover:text-white hover:bg-gray-800'
                    }
                    ${item.comingSoon ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                  onClick={(e) => item.comingSoon && e.preventDefault()}
                >
                  <Icon className="w-5 h-5 flex-shrink-0" />
                  <AnimatePresence>
                    {!collapsed && (
                      <motion.div
                        initial={{ opacity: 0, width: 0 }}
                        animate={{ opacity: 1, width: 'auto' }}
                        exit={{ opacity: 0, width: 0 }}
                        className="flex items-center space-x-2"
                      >
                        <span className="font-medium">{item.label}</span>
                        {item.comingSoon && (
                          <span className="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-0.5 rounded">
                            Soon
                          </span>
                        )}
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>
    </motion.div>
  );
};