import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Plus, Search, Grid, List, Filter, Pin, MoreVertical, Edit, Trash } from 'lucide-react';
import { TopicBlock } from '../types';

export const Vault: React.FC = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [showNewBlockForm, setShowNewBlockForm] = useState(false);

  // Mock data
  const [blocks, setBlocks] = useState<TopicBlock[]>([
    {
      id: '1',
      userId: 'user1',
      name: 'Ancient History',
      description: 'Exploring civilizations of the past',
      color: 'bg-amber-500',
      imageUrl: 'https://images.pexels.com/photos/7192668/pexels-photo-7192668.jpeg',
      tags: ['History', 'Ancient', 'Civilizations'],
      isPinned: true,
      subtopicCount: 12,
      createdAt: '2024-01-15T10:00:00Z',
      updatedAt: '2024-01-20T15:30:00Z'
    },
    {
      id: '2',
      userId: 'user1',
      name: 'Quantum Physics',
      description: 'Understanding the quantum world',
      color: 'bg-blue-500',
      imageUrl: 'https://images.pexels.com/photos/8474772/pexels-photo-8474772.jpeg',
      tags: ['Physics', 'Quantum', 'Science'],
      isPinned: false,
      subtopicCount: 8,
      createdAt: '2024-01-10T14:00:00Z',
      updatedAt: '2024-01-18T09:15:00Z'
    },
    {
      id: '3',
      userId: 'user1',
      name: 'Renaissance Art',
      description: 'Masterpieces of the Renaissance period',
      color: 'bg-purple-500',
      imageUrl: 'https://images.pexels.com/photos/2252924/pexels-photo-2252924.jpeg',
      tags: ['Art', 'Renaissance', 'Culture'],
      isPinned: false,
      subtopicCount: 15,
      createdAt: '2024-01-08T11:30:00Z',
      updatedAt: '2024-01-19T16:45:00Z'
    }
  ]);

  const filteredBlocks = blocks.filter(block =>
    block.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    block.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const sortedBlocks = [...filteredBlocks].sort((a, b) => {
    if (a.isPinned && !b.isPinned) return -1;
    if (!a.isPinned && b.isPinned) return 1;
    return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
  });

  const BlockCard = ({ block }: { block: TopicBlock }) => (
    <motion.div
      layout
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -4, boxShadow: '0 20px 40px rgba(0,0,0,0.3)' }}
      className="bg-gray-800/50 backdrop-blur-sm rounded-xl overflow-hidden border border-gray-700 hover:border-gray-600 transition-all duration-200 group cursor-pointer"
    >
      <div className="relative">
        <img
          src={block.imageUrl}
          alt={block.name}
          className="w-full h-32 object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
        {block.isPinned && (
          <div className="absolute top-3 right-3">
            <Pin className="w-4 h-4 text-yellow-400 fill-current" />
          </div>
        )}
        <div className="absolute bottom-3 left-3 right-3">
          <h3 className="text-white font-semibold text-lg">{block.name}</h3>
        </div>
      </div>
      
      <div className="p-4">
        <p className="text-gray-400 text-sm mb-3 line-clamp-2">{block.description}</p>
        
        <div className="flex flex-wrap gap-1 mb-3">
          {block.tags.map((tag) => (
            <span
              key={tag}
              className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded-full"
            >
              {tag}
            </span>
          ))}
        </div>
        
        <div className="flex items-center justify-between text-sm text-gray-400">
          <span>{block.subtopicCount} notes</span>
          <div className="flex items-center space-x-2">
            <span>{new Date(block.updatedAt).toLocaleDateString()}</span>
            <button className="p-1 rounded hover:bg-gray-700 opacity-0 group-hover:opacity-100 transition-opacity">
              <MoreVertical className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">My Vault</h1>
          <p className="text-gray-400 mt-1">Organize your knowledge into topics</p>
        </div>
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={() => setShowNewBlockForm(true)}
          className="flex items-center space-x-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          <Plus className="w-5 h-5" />
          <span>New Topic</span>
        </motion.button>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="w-5 h-5 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search topics..."
              className="bg-gray-800 border border-gray-700 rounded-lg pl-10 pr-4 py-2 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          <button className="flex items-center space-x-2 px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-gray-300 hover:text-white transition-colors">
            <Filter className="w-4 h-4" />
            <span>Filter</span>
          </button>
        </div>

        <div className="flex items-center space-x-2 bg-gray-800 rounded-lg p-1">
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded ${viewMode === 'grid' ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'}`}
          >
            <Grid className="w-4 h-4" />
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded ${viewMode === 'list' ? 'bg-purple-600 text-white' : 'text-gray-400 hover:text-white'}`}
          >
            <List className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Blocks Grid */}
      <AnimatePresence>
        <motion.div
          layout
          className={viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
          }
        >
          {sortedBlocks.map((block) => (
            <BlockCard key={block.id} block={block} />
          ))}
        </motion.div>
      </AnimatePresence>

      {sortedBlocks.length === 0 && (
        <div className="text-center py-12">
          <div className="w-24 h-24 bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
            <Plus className="w-12 h-12 text-gray-600" />
          </div>
          <h3 className="text-xl font-medium text-white mb-2">No topics yet</h3>
          <p className="text-gray-400 mb-4">Create your first topic to start organizing your knowledge</p>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowNewBlockForm(true)}
            className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Create Topic
          </motion.button>
        </div>
      )}

      {/* New Block Form Modal */}
      <AnimatePresence>
        {showNewBlockForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowNewBlockForm(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-gray-800 rounded-xl p-6 w-full max-w-md border border-gray-700"
              onClick={(e) => e.stopPropagation()}
            >
              <h2 className="text-xl font-semibold text-white mb-4">Create New Topic</h2>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Topic Name</label>
                  <input
                    type="text"
                    placeholder="e.g., Ancient History"
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Description</label>
                  <textarea
                    placeholder="Brief description of this topic..."
                    rows={3}
                    className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowNewBlockForm(false)}
                    className="flex-1 bg-gray-700 hover:bg-gray-600 text-white py-2 rounded-lg transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={() => setShowNewBlockForm(false)}
                    className="flex-1 bg-purple-600 hover:bg-purple-700 text-white py-2 rounded-lg transition-colors"
                  >
                    Create
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};