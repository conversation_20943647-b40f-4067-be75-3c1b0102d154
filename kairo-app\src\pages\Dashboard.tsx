import React from 'react';
import { motion } from 'framer-motion';
import { TrendingUp, BookOpen, Target, Zap, Plus, Award, Clock, Brain } from 'lucide-react';
import { useAuth } from '../hooks/useAuth';

export const Dashboard: React.FC = () => {
  const { user } = useAuth();

  const stats = [
    { label: 'Total XP', value: user?.xp || 0, icon: Zap, color: 'text-yellow-500' },
    { label: 'Level', value: user?.level || 1, icon: TrendingUp, color: 'text-blue-500' },
    { label: 'Notes Published', value: 23, icon: BookOpen, color: 'text-green-500' },
    { label: 'Quiz Accuracy', value: '87%', icon: Target, color: 'text-purple-500' },
  ];

  const recentNotes = [
    { title: 'Ancient Egyptian Medicine', topic: 'History', timestamp: '2 hours ago' },
    { title: 'Quantum Mechanics Basics', topic: 'Physics', timestamp: '1 day ago' },
    { title: 'Renaissance Art Movement', topic: 'Art History', timestamp: '3 days ago' },
  ];

  const achievements = [
    { name: 'First Note', icon: '📝', unlocked: true },
    { name: 'Quiz Master', icon: '🧠', unlocked: true },
    { name: 'Streak Keeper', icon: '🔥', unlocked: false },
    { name: 'Knowledge Vault', icon: '📚', unlocked: false },
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Welcome back, {user?.displayName}!</h1>
          <p className="text-gray-400 mt-1">Ready to continue your learning journey?</p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <motion.div
              key={stat.label}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700 hover:border-gray-600 transition-all duration-200"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">{stat.label}</p>
                  <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                </div>
                <Icon className={`w-8 h-8 ${stat.color}`} />
              </div>
            </motion.div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Access */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700"
        >
          <h2 className="text-xl font-semibold text-white mb-4">Quick Access</h2>
          <div className="space-y-3">
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full flex items-center space-x-3 p-3 bg-purple-600/20 border border-purple-500/30 rounded-lg hover:bg-purple-600/30 transition-colors"
            >
              <Plus className="w-5 h-5 text-purple-400" />
              <span className="text-white">Add New Note</span>
            </motion.button>
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full flex items-center space-x-3 p-3 bg-blue-600/20 border border-blue-500/30 rounded-lg hover:bg-blue-600/30 transition-colors"
            >
              <Brain className="w-5 h-5 text-blue-400" />
              <span className="text-white">Take Quiz</span>
            </motion.button>
          </div>
        </motion.div>

        {/* Recent Notes */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700"
        >
          <h2 className="text-xl font-semibold text-white mb-4">Recent Notes</h2>
          <div className="space-y-3">
            {recentNotes.map((note, index) => (
              <div key={index} className="p-3 bg-gray-700/30 rounded-lg">
                <h3 className="text-white font-medium text-sm">{note.title}</h3>
                <div className="flex items-center justify-between mt-1">
                  <span className="text-purple-400 text-xs">{note.topic}</span>
                  <span className="text-gray-400 text-xs flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    {note.timestamp}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Achievements */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-gray-800/50 backdrop-blur-sm rounded-xl p-6 border border-gray-700"
        >
          <h2 className="text-xl font-semibold text-white mb-4">Achievements</h2>
          <div className="grid grid-cols-2 gap-3">
            {achievements.map((achievement, index) => (
              <div
                key={index}
                className={`p-3 rounded-lg border ${
                  achievement.unlocked
                    ? 'bg-yellow-500/10 border-yellow-500/30'
                    : 'bg-gray-700/30 border-gray-600'
                }`}
              >
                <div className="text-center">
                  <div className="text-2xl mb-1">{achievement.icon}</div>
                  <p className={`text-xs ${achievement.unlocked ? 'text-yellow-400' : 'text-gray-400'}`}>
                    {achievement.name}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Did You Know Panel */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 backdrop-blur-sm rounded-xl p-6 border border-purple-500/30"
      >
        <h2 className="text-xl font-semibold text-white mb-2">💡 Did You Know?</h2>
        <p className="text-gray-300">
          Taking regular quizzes can improve your long-term retention by up to 40%! 
          Try creating a quiz from your recent notes to reinforce your learning.
        </p>
      </motion.div>
    </div>
  );
};